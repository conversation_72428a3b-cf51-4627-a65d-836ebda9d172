package ttfund.web.fortuneservice.service;

import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import ttfund.web.fortuneservice.FortuneServiceApplication;
import ttfund.web.fortuneservice.model.dto.MultiMonthlyReportRequestDTO;
import ttfund.web.fortuneservice.model.dto.MultiMonthlyReportResponseDTO;

import java.util.*;

/**
 * VIP多机构月度报告总结服务测试类
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = FortuneServiceApplication.class)
@Slf4j
public class VIPMultiMonthlyReportSummaryServiceTest {

    @Autowired
    private VIPMultiMonthlyReportSummaryService vipMultiMonthlyReportSummaryService;

    @Test
    public void testGenerateMultiMonthlyReportSummary() {
        try {
            log.info("开始测试VIP多机构月度报告总结服务");

            // 执行测试
            boolean result = vipMultiMonthlyReportSummaryService.generateMultiMonthlyReportSummary("test");

            log.info("测试结果：{}", result);

            // 验证结果
            assert result : "多机构月度报告总结生成应该成功";

            log.info("VIP多机构月度报告总结服务测试完成");

        } catch (Exception e) {
            log.error("测试VIP多机构月度报告总结服务异常", e);
            throw e;
        }
    }
}