package ttfund.web.fortuneservice.model.dto;

import lombok.Data;
import java.util.List;

/**
 * 月度报告请求DTO
 */
@Data
public class MonthlyReportRequestDTO {
    
    /**
     * 机构名称
     */
    private String institutionName;
    
    /**
     * 任务月份 (格式: yyyy-MM)
     */
    private String taskMonth;
    
    /**
     * 大类资产
     */
    private List<CategoryAsset> categoryAssets;

    /**
     * 行业数据
     */
    private List<Industry> industries;
    
    /**
     * 资产配置
     */
    private List<AssetAllocation> assetAllocations;
    
    /**
     * 宏观经济
     */
    private List<MacroEconomic> macroEconomics;
    
    /**
     * 截止时间
     */
    private String deadline;
    
    @Data
    public static class CategoryAsset {
        /**
         * 大类资产类型
         */
        private String categoryType;

        /**
         * 大类资产名称
         */
        private String categoryName;

        /**
         * 大类资产代码
         */
        private String categoryCode;

        /**
         * 观点-历史总结
         */
        private String historicalSummary;

        /**
         * 观点-未来预期
         */
        private String futureExpectation;
    }

    @Data
    public static class Industry {
        /**
         * 行业类型
         */
        private String industryType;

        /**
         * 行业名称
         */
        private String industryName;

        /**
         * 行业代码
         */
        private String industryCode;

        /**
         * 观点-历史总结
         */
        private String historicalSummary;

        /**
         * 观点-未来预期
         */
        private String futureExpectation;
    }
    
    @Data
    public static class AssetAllocation {
        /**
         * 所属类型
         */
        private String assetType;
        
        /**
         * 资产名称
         */
        private String assetName;
        
        /**
         * 低风险占比
         */
        private Double lowRiskRatio;
        
        /**
         * 中风险占比
         */
        private Double mediumRiskRatio;
        
        /**
         * 高风险占比
         */
        private Double highRiskRatio;

        /**
         * 配置理由
         */
        private String reason;
    }
    
    @Data
    public static class MacroEconomic {
        /**
         * 方面详情
         */
        private String aspectDetail;
        
        /**
         * 项目列表
         */
        private List<MacroEconomicItem> items;
    }
    
    @Data
    public static class MacroEconomicItem {
        /**
         * 小标题
         */
        private String subtitle;
        
        /**
         * 内容
         */
        private String content;
    }
}
