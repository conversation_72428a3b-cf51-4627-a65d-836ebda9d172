package ttfund.web.fortuneservice.model.dto;

import lombok.Data;
import java.util.Date;

/**
 * VIP任务数据DTO
 */
@Data
public class VIPTaskDataDTO {

    /**
     * 财富号ID
     */
    private String cfhid;

    /**
     * 任务配置ID
     */
    private String taskConfigId;

    /**
     * 任务周期标记
     */
    private String taskCycleMark;

    /**
     * 机构名称
     */
    private String institutionName;
    
    /**
     * 任务月份
     */
    private String taskMonth;
    
    /**
     * 所属类型（大类资产）
     */
    private String categoryType;

    /**
     * 大类资产名称
     */
    private String categoryName;

    /**
     * 大类资产代码
     */
    private String categoryCode;

    /**
     * 行业类型
     */
    private String industryType;

    /**
     * 行业名称
     */
    private String industryName;

    /**
     * 行业代码
     */
    private String industryCode;
    
    /**
     * 观点-历史总结
     */
    private String historicalSummary;
    
    /**
     * 观点-未来预期
     */
    private String futureExpectation;
    
    /**
     * 资产类型
     */
    private String assetType;
    
    /**
     * 资产名称
     */
    private String assetName;
    
    /**
     * 低风险占比
     */
    private String lowRiskRatio;
    
    /**
     * 中风险占比
     */
    private String mediumRiskRatio;
    
    /**
     * 高风险占比
     */
    private String highRiskRatio;

    /**
     * 资产配置理由
     */
    private String reason;
    
    /**
     * 主题类型
     */
    private String subjectType;
    
    /**
     * 主题标题
     */
    private String subjectTitle;
    
    /**
     * 观点预测
     */
    private String pointPrediction;
    
    /**
     * 题目ID
     */
    private String subjectId;

    /**
     * 截止时间
     */
    private Date deadline;
}
