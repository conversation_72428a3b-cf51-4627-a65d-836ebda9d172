package ttfund.web.fortuneservice.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import ttfund.web.fortuneservice.dao.CfhMongodbDao;
import ttfund.web.fortuneservice.constant.CFHMongodbConstant;
import ttfund.web.fortuneservice.constant.VIPAIReportConstant;
import com.mongodb.BasicDBObject;
import org.bson.Document;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import ttfund.web.fortuneservice.dao.sqlserver.VIPAIReportDetailMapper;
import ttfund.web.fortuneservice.dao.sqlserver.VIPAIReportMapper;
import ttfund.web.fortuneservice.model.entity.VIPAIReport;
import ttfund.web.fortuneservice.model.entity.VIPAIReportDetail;
import ttfund.web.fortuneservice.model.mongo.VIPAIReportMongo;
import ttfund.web.fortuneservice.model.mongo.VIPAIReportDetailMongo;
import ttfund.web.fortuneservice.service.VIPAIReportSyncService;
import ttfund.web.fortuneservice.config.App;

import java.util.*;
import java.util.stream.Collectors;

/**
 * VIP AI报告数据同步服务实现类
 */
@Service
@Slf4j
public class VIPAIReportSyncServiceImpl implements VIPAIReportSyncService {

    @Autowired
    private VIPAIReportMapper vipAIReportMapper;

    @Autowired
    private VIPAIReportDetailMapper vipAIReportDetailMapper;

    @Autowired
    private CfhMongodbDao cfhMongodbDao;

    @Autowired
    private App app;

    @Override
    public boolean syncVIPAIReportToMongoDB(Date breakpointTime) {
        try {
            log.info("开始同步VIP AI报告数据到MongoDB，断点时间：{}", breakpointTime);

            // 1. 查询需要同步的报告数据（基于Tb_VIPAIReport表的更新时间）
            List<VIPAIReport> reportsToSync = getReportsToSync(breakpointTime);

            // 3. 收集需要同步的报告ID
            Set<String> reportIdsToSync = new HashSet<>();

            // 从报告表中收集需要同步的报告ID
            if (!CollectionUtils.isEmpty(reportsToSync)) {
                reportIdsToSync.addAll(reportsToSync.stream()
                    .map(VIPAIReport::getId)
                    .collect(Collectors.toSet()));
            }

            if (reportIdsToSync.isEmpty()) {
                log.info("没有需要同步的报告数据");
                return true;
            }

            log.info("找到{}条需要同步的报告数据", reportIdsToSync.size());

            // 4. 处理每个报告
            int successCount = 0;
            for (String reportId : reportIdsToSync) {
                try {
                    // 获取完整的报告信息
                    VIPAIReport report = vipAIReportMapper.selectByPrimaryKey(reportId);
                    if (report == null) {
                        log.warn("报告不存在，跳过同步，reportId：{}", reportId);
                        continue;
                    }

                    // 获取报告详细信息
                    List<VIPAIReportDetail> details = vipAIReportDetailMapper.selectByReportId(reportId);

                    // 转换为MongoDB文档
                    VIPAIReportMongo mongoReport = convertToMongoDocument(report, details);

                    // 保存到MongoDB
                    saveToMongoDB(mongoReport);
                    successCount++;

                    log.debug("成功同步报告：{}", reportId);
                } catch (Exception e) {
                    log.error("同步报告失败，报告ID：{}，错误：{}", reportId, e.getMessage(), e);
                }
            }

            log.info("VIP AI报告数据同步完成，成功同步：{}/{}", successCount, reportIdsToSync.size());
            return successCount > 0;

        } catch (Exception e) {
            log.error("同步VIP AI报告数据到MongoDB异常", e);
            return false;
        }
    }

    @Override
    public Date getLatestUpdateTime() {
        try {
            // 从MongoDB中获取VIPAIReport集合的最新更新时间
            BasicDBObject query = new BasicDBObject(); // 查询所有记录
            BasicDBObject field = new BasicDBObject("updateTime", 1).append("_id", 0); // 只返回updateTime字段
            BasicDBObject sort = new BasicDBObject("updateTime", -1); // 按updateTime降序排列

            List<Document> results = app.getCfhMongodbRead().query(
                CFHMongodbConstant.DB_TT_FUND_CFH,
                CFHMongodbConstant.TB_VIP_AI_REPORT,
                query,
                field,
                sort,
                1, // 只取第一条记录
                Document.class
            );

            if (!results.isEmpty()) {
                Object updateTimeObj = results.get(0).get("updateTime");
                if (updateTimeObj instanceof Date) {
                    return (Date) updateTimeObj;
                }
            }

            return null;
        } catch (Exception e) {
            log.error("从MongoDB获取最新更新时间失败", e);
            return null;
        }
    }

    /**
     * 从指定表获取最新更新时间
     */
    private Date getLatestUpdateTimeFromTable(String tableName) {
        try {
            String sql = "SELECT MAX(UpdateTime) as MaxUpdateTime FROM " + tableName;
            List<Map> maps = app.getSqlServer().executeQuery(sql, new ArrayList<>());

            if (!CollectionUtils.isEmpty(maps) && maps.get(0).get("MaxUpdateTime") != null) {
                return (Date) maps.get(0).get("MaxUpdateTime");
            }
            return null;
        } catch (Exception e) {
            log.error("从表{}获取最新更新时间失败", tableName, e);
            return null;
        }
    }

    /**
     * 获取需要同步的报告数据（基于Tb_VIPAIReport表）
     */
    private List<VIPAIReport> getReportsToSync(Date breakpointTime) {
        String sql = "SELECT ID, CFHID, TaskConfigID, TaskCycleMark, ReportDate, Status, [Index], CreateTime, UpdateTime " +
                     "FROM Tb_VIPAIReport " +
                     "WHERE UpdateTime > ? " +
                     "ORDER BY UpdateTime ASC";

        List<Object> params = new ArrayList<>();
        params.add(breakpointTime);

        List<Map> maps = app.getSqlServer().executeQuery(sql, params);
        if (!maps.isEmpty()) {
            return JSON.parseArray(JSON.toJSONString(maps), VIPAIReport.class);
        }
        return new ArrayList<>();
    }



    /**
     * 转换为MongoDB文档
     */
    private VIPAIReportMongo convertToMongoDocument(VIPAIReport report, List<VIPAIReportDetail> details) {
        VIPAIReportMongo mongoReport = new VIPAIReportMongo();

        // 设置_id字段作为MongoDB主键
        String reportId = report.getId();
        if (reportId == null || reportId.trim().isEmpty()) {
            throw new RuntimeException("VIPAIReport的ID不能为null");
        }

        mongoReport.set_id(reportId);
        mongoReport.setCfhid(report.getCfhid());
        mongoReport.setTaskConfigId(report.getTaskConfigId());
        mongoReport.setTaskCycleMark(report.getTaskCycleMark());
        mongoReport.setReportDate(report.getReportDate());
        mongoReport.setStatus(report.getStatus());
        mongoReport.setIndex(report.getIndex()); // 同步批次内序号
        mongoReport.setCreateTime(report.getCreateTime());
        mongoReport.setUpdateTime(report.getUpdateTime());

        // 处理详细信息 - 转换为JSON字符串避免嵌套对象编解码器问题
        if (!CollectionUtils.isEmpty(details)) {
            try {
                List<VIPAIReportDetailMongo> mongoDetails = new ArrayList<>();
                for (VIPAIReportDetail detail : details) {
                    VIPAIReportDetailMongo mongoDetail = convertDetailToMongo(detail);
                    mongoDetails.add(mongoDetail);
                }
                // 将整个Details列表转换为JSON字符串
                String detailsJson = JSON.toJSONString(mongoDetails);
                mongoReport.setDetails(detailsJson);
            } catch (Exception e) {
                log.warn("转换Details为JSON失败，使用空数组，reportId：{}", report.getId(), e);
                mongoReport.setDetails(VIPAIReportConstant.Json.EMPTY_ARRAY);
            }
        } else {
            mongoReport.setDetails(VIPAIReportConstant.Json.EMPTY_ARRAY);
        }

        return mongoReport;
    }

    /**
     * 转换详细信息为MongoDB文档
     */
    private VIPAIReportDetailMongo convertDetailToMongo(VIPAIReportDetail detail) {
        VIPAIReportDetailMongo mongoDetail = new VIPAIReportDetailMongo();
        mongoDetail.setId(detail.getId());
        mongoDetail.setReportId(detail.getReportId());
        mongoDetail.setTitle(detail.getTitle());
        mongoDetail.setReferences(detail.getReferences());
        mongoDetail.setReportType(detail.getReportType());
        mongoDetail.setType(detail.getType());
        mongoDetail.setCreateTime(detail.getCreateTime());
        mongoDetail.setUpdateTime(detail.getUpdateTime());

        // 根据类型处理内容
        if (VIPAIReportConstant.ReportType.MULTI_ORG.equals(detail.getType())) {
            // 多机构数据：处理JSON内容，添加ReportID字段
            mongoDetail.setContent(processMultiOrgContent(detail.getContent(), detail.getReportId()));
        } else if (VIPAIReportConstant.ReportType.SINGLE_ORG.equals(detail.getType())) {
            // 单机构数据：添加originalQA字段，转换为JSON字符串
            mongoDetail.setContent(detail.getContent());
            List<VIPAIReportDetailMongo.OriginalQAItem> originalQAList = getSingleOrgOriginalQA(detail.getId());
            if (!CollectionUtils.isEmpty(originalQAList)) {
                mongoDetail.setOriginalQA(JSON.toJSONString(originalQAList));
            } else {
                mongoDetail.setOriginalQA(VIPAIReportConstant.Json.EMPTY_ARRAY);
            }
        } else {
            // 其他类型（如OVERALL等）直接保存
            mongoDetail.setContent(detail.getContent());
        }

        return mongoDetail;
    }

    /**
     * 处理多机构内容，添加ReportID字段到sourceReferences
     */
    private Object processMultiOrgContent(String content, String reportId) {
        if (StringUtils.isEmpty(content)) {
            return content;
        }

        try {
            JSONObject contentJson = JSON.parseObject(content);

            // 处理similarities
            if (contentJson.containsKey("similarities")) {
                JSONArray similarities = contentJson.getJSONArray("similarities");
                processSimilarities(similarities, reportId);
            }

            // 处理differences
            if (contentJson.containsKey("differences")) {
                JSONArray differences = contentJson.getJSONArray("differences");
                processDifferences(differences, reportId);
            }

            return contentJson;
        } catch (Exception e) {
            log.error("处理多机构内容失败，reportId：{}，错误：{}", reportId, e.getMessage(), e);
            return content; // 返回原始内容
        }
    }

    /**
     * 处理similarities数组，添加ReportID到sourceReferences
     */
    private void processSimilarities(JSONArray similarities, String reportId) {
        if (similarities == null) {
            return;
        }

        for (int i = 0; i < similarities.size(); i++) {
            JSONObject similarity = similarities.getJSONObject(i);
            if (similarity.containsKey("sourceReferences")) {
                JSONArray sourceReferences = similarity.getJSONArray("sourceReferences");
                addReportIdToSourceReferences(sourceReferences, reportId);
            }
        }
    }

    /**
     * 处理differences数组，添加ReportID到viewpoints的sourceReferences
     */
    private void processDifferences(JSONArray differences, String reportId) {
        if (differences == null) {
            return;
        }

        for (int i = 0; i < differences.size(); i++) {
            JSONObject difference = differences.getJSONObject(i);
            if (difference.containsKey("viewpoints")) {
                JSONArray viewpoints = difference.getJSONArray("viewpoints");
                if (viewpoints != null) {
                    for (int j = 0; j < viewpoints.size(); j++) {
                        JSONObject viewpoint = viewpoints.getJSONObject(j);
                        if (viewpoint.containsKey("sourceReferences")) {
                            JSONArray sourceReferences = viewpoint.getJSONArray("sourceReferences");
                            addReportIdToSourceReferences(sourceReferences, reportId);
                        }
                    }
                }
            }
        }
    }

    /**
     * 向sourceReferences数组中的每个元素添加ReportID字段
     */
    private void addReportIdToSourceReferences(JSONArray sourceReferences, String currentReportId) {
        if (sourceReferences == null) {
            return;
        }

        for (int i = 0; i < sourceReferences.size(); i++) {
            JSONObject sourceRef = sourceReferences.getJSONObject(i);
            String cfhId = sourceRef.getString("cfhId");

            if (StringUtils.isNotEmpty(cfhId)) {
                // 通过cfhId、TaskConfigID、TaskCycleMark查询对应的ReportID
                String reportId = getReportIdByCfhId(cfhId, currentReportId);
                if (StringUtils.isNotEmpty(reportId)) {
                    sourceRef.put("reportId", reportId);
                }
            }
        }
    }

    /**
     * 通过cfhId和当前reportId获取对应的ReportID
     */
    private String getReportIdByCfhId(String cfhId, String currentReportId) {
        try {
            // 首先获取当前报告的TaskConfigID和TaskCycleMark
            String sql1 = "SELECT TaskConfigID, TaskCycleMark FROM Tb_VIPAIReport WHERE ID = ?";
            List<Object> params1 = Arrays.asList(currentReportId);
            List<Map> currentReportMaps = app.getSqlServer().executeQuery(sql1, params1);

            if (CollectionUtils.isEmpty(currentReportMaps)) {
                return null;
            }

            Map currentReportMap = currentReportMaps.get(0);
            String taskConfigId = (String) currentReportMap.get("TaskConfigID");
            String taskCycleMark = (String) currentReportMap.get("TaskCycleMark");

            // 通过cfhId、TaskConfigID、TaskCycleMark查询对应的ReportID
            String sql2 = "SELECT ID FROM Tb_VIPAIReport WHERE CFHID = ? AND TaskConfigID = ? AND TaskCycleMark = ?";
            List<Object> params2 = Arrays.asList(cfhId, taskConfigId, taskCycleMark);
            List<Map> reportMaps = app.getSqlServer().executeQuery(sql2, params2);

            if (!CollectionUtils.isEmpty(reportMaps)) {
                return (String) reportMaps.get(0).get("ID");
            }

        } catch (Exception e) {
            log.error("获取ReportID失败，cfhId：{}，currentReportId：{}，错误：{}", cfhId, currentReportId, e.getMessage(), e);
        }

        return null;
    }

    /**
     * 获取单机构的原始问答数据
     */
    private List<VIPAIReportDetailMongo.OriginalQAItem> getSingleOrgOriginalQA(String detailId) {
        try {
            String sql = "SELECT report.CFHID, report.TaskConfigID, report.TaskCycleMark, detail.ID, " +
                        "sa.SubjectTitle, sa.SubjectCode, sa.pointPrediction, sa.SubjectID, sa.SubjectType, sa.TaskSubjectType, " +
                        "sa.sort, sa.UpdateTime " +
                        "FROM Tb_VIPAIReport report " +
                        "LEFT JOIN Tb_VIPAIReportDetail detail ON report.ID = detail.ReportID " +
                        "LEFT JOIN (" +
                        "    SELECT ts.ID as SubjectID, ts.TaskConfigID, ts.TaskCycleMark, ts.SubjectType, " +
                        "           ts.SubjectTitle, ts.SubjectCode, ts.TaskSubjectType, ts.sort, ts.UpdateTime, " +
                        "           ta.ID as AnswerID, ta.pointPrediction, ta.CFHID " +
                        "    FROM Tb_VIPTaskSubject ts " +
                        "    LEFT JOIN Tb_VIPSubjectAnswer ta ON ts.ID = ta.SubjectID" +
                        ") sa ON report.CFHID = sa.CFHID AND report.TaskConfigID = sa.TaskConfigID AND report.TaskCycleMark = sa.TaskCycleMark " +
                        "WHERE detail.Type = ? AND detail.ID = ? " +
                        "ORDER BY sa.sort ASC, sa.UpdateTime ASC";

            List<Object> params = Arrays.asList(VIPAIReportConstant.ReportType.SINGLE_ORG, detailId);
            List<Map> maps = app.getSqlServer().executeQuery(sql, params);

            List<VIPAIReportDetailMongo.OriginalQAItem> originalQAList = new ArrayList<>();

            if (!CollectionUtils.isEmpty(maps)) {
                for (Map map : maps) {
                    if (map.get("SubjectTitle") != null && map.get("pointPrediction") != null) {
                        VIPAIReportDetailMongo.OriginalQAItem qaItem =
                            new VIPAIReportDetailMongo.OriginalQAItem();
                        qaItem.setSubjectId((String) map.get("SubjectID"));
                        qaItem.setSubjectType((Integer) map.get("SubjectType"));
                        qaItem.setTaskSubjectType((String) map.get("TaskSubjectType"));
                        qaItem.setSubjectTitle((String) map.get("SubjectTitle"));
                        qaItem.setSubjectCode((String) map.get("SubjectCode"));
                        qaItem.setPointPrediction((String) map.get("pointPrediction"));
                        originalQAList.add(qaItem);
                    }
                }
            }

            return originalQAList;

        } catch (Exception e) {
            log.error("获取单机构原始问答数据失败，detailId：{}，错误：{}", detailId, e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    /**
     * 保存数据到MongoDB
     */
    private void saveToMongoDB(VIPAIReportMongo mongoReport) {
        try {
            if (mongoReport.get_id() == null || mongoReport.get_id().trim().isEmpty()) {
                throw new RuntimeException("MongoDB文档的_id不能为null");
            }

            List<VIPAIReportMongo> dataList = Arrays.asList(mongoReport);
            cfhMongodbDao.insertOrUpdate(dataList, CFHMongodbConstant.DB_TT_FUND_CFH, CFHMongodbConstant.TB_VIP_AI_REPORT);

        } catch (Exception e) {
            log.error("保存报告到MongoDB异常，_id：{}，错误：{}", mongoReport.get_id(), e.getMessage(), e);
            throw e;
        }
    }


}
