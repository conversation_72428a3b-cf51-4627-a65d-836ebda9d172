package ttfund.web.fortuneservice.service;

import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import ttfund.web.fortuneservice.FortuneServiceApplication;

/**
 * VIP月度报告总结服务手动测试类
 * 用于手动执行和调试服务
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = FortuneServiceApplication.class)
@Slf4j
public class VIPMonthlyReportSummaryManualTest {

    @Autowired
    private VIPMonthlyReportSummaryService vipMonthlyReportSummaryService;

    /**
     * 手动测试主方法
     * 可以直接运行此方法来测试服务
     */
    @Test
    public void manualTestGenerateMonthlyReportSummary() {
        log.info("=== 开始手动测试VIP月度报告总结服务 ===");
        
        try {
            // 执行服务方法
            log.info("调用generateMonthlyReportSummary方法...");
            boolean result = vipMonthlyReportSummaryService.generateMonthlyReportSummary("manual-test");
            
            log.info("服务执行结果：{}", result);
            
            if (result) {
                log.info("✅ 服务执行成功！");
            } else {
                log.warn("⚠️ 服务执行返回false，可能没有数据需要处理");
            }
            
        } catch (Exception e) {
            log.error("❌ 服务执行异常：", e);
            
            // 分析异常类型
            if (e.getMessage() != null) {
                if (e.getMessage().contains("database") || e.getMessage().contains("SQL")) {
                    log.error("数据库相关异常，请检查数据库连接和表结构");
                } else if (e.getMessage().contains("http") || e.getMessage().contains("connection")) {
                    log.error("网络连接异常，请检查AI接口地址和网络连接");
                } else {
                    log.error("其他异常：{}", e.getMessage());
                }
            }
        }
        
        log.info("=== 手动测试完成 ===");
    }
}
