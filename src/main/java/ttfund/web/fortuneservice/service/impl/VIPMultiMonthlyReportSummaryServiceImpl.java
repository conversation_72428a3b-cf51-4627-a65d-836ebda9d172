package ttfund.web.fortuneservice.service.impl;

import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import ttfund.web.fortuneservice.config.App;
import ttfund.web.fortuneservice.manager.MultiMonthlyReportAIApiService;
import ttfund.web.fortuneservice.model.dto.*;
import ttfund.web.fortuneservice.service.VIPMultiAIReportDataService;
import ttfund.web.fortuneservice.service.VIPMultiMonthlyReportSummaryService;
import ttfund.web.fortuneservice.utils.TimeUtil;

import java.util.*;
import java.util.stream.Collectors;

/**
 * VIP多机构月度报告总结服务实现类
 */
@Service
@Slf4j
public class VIPMultiMonthlyReportSummaryServiceImpl implements VIPMultiMonthlyReportSummaryService {

    @Autowired
    private App app;

    @Autowired
    private MultiMonthlyReportAIApiService multiMonthlyReportAIApiService;

    @Autowired
    private VIPMultiAIReportDataService vipMultiAIReportDataService;

    @Override
    public boolean generateMultiMonthlyReportSummary(String param) {
        try {
            log.info("开始执行VIP多机构月度报告总结任务，参数：{}", param);

            // 1. 查询数据库获取VIP任务数据
            List<VIPTaskDataDTO> taskDataList = queryVIPTaskData();
            if (CollectionUtils.isEmpty(taskDataList)) {
                log.info("未查询到需要处理的VIP任务数据");
                return true;
            }

            // 2. 按月份分组处理（多机构按月份聚合）
            Map<String, List<VIPTaskDataDTO>> groupedByMonth = groupDataByMonth(taskDataList);

            // 3. 为每个月份生成多机构报告总结
            int successCount = 0;
            int skippedCount = 0;
            int totalCount = groupedByMonth.size();

            for (Map.Entry<String, List<VIPTaskDataDTO>> entry : groupedByMonth.entrySet()) {
                String month = entry.getKey();
                List<VIPTaskDataDTO> monthData = entry.getValue();

                try {
                    log.info("处理月份：{}，数据条数：{}", month, monthData.size());

                    // 构建多机构请求数据
                    MultiMonthlyReportRequestDTO request = buildMultiRequest(monthData);
                    if (request == null || CollectionUtils.isEmpty(request.getInstitutions())) {
                        log.warn("构建多机构请求数据失败，跳过月份：{}", month);

                        // 虽然构建请求失败，但仍需要更新AIstatus标记为已处理
                        String taskConfigId = monthData.isEmpty() ? null : monthData.get(0).getTaskConfigId();
                        String taskCycleMark = monthData.isEmpty() ? null : monthData.get(0).getTaskCycleMark();
                        updateMultiTaskListAIStatus(taskConfigId, taskCycleMark, 0);

                        skippedCount++;
                        continue;
                    }

                    // 检查机构数量，如果只有一个机构则跳过
                    if (request.getInstitutions().size() < 2) {
                        log.info("月份：{}，只包含{}个机构，跳过多机构报告生成", month, request.getInstitutions().size());

                        // 虽然跳过AI处理，但仍需要更新AIstatus标记为已处理
                        String taskConfigId = monthData.isEmpty() ? null : monthData.get(0).getTaskConfigId();
                        String taskCycleMark = monthData.isEmpty() ? null : monthData.get(0).getTaskCycleMark();
                        updateMultiTaskListAIStatus(taskConfigId, taskCycleMark, 0);

                        skippedCount++;
                        continue;
                    }

                    log.info("月份：{}，包含机构数量：{}，开始生成多机构报告", month, request.getInstitutions().size());

                    // 从月份数据中获取taskConfigId和taskCycleMark（使用第一条数据）
                    String taskConfigId = monthData.isEmpty() ? null : monthData.get(0).getTaskConfigId();
                    String taskCycleMark = monthData.isEmpty() ? null : monthData.get(0).getTaskCycleMark();

                    // 调用AI接口
                    MultiMonthlyReportResponseDTO response = callMultiAIServiceAsync(request, taskConfigId, taskCycleMark);
                    if (response != null) {

                        // 保存结果到数据库
                        vipMultiAIReportDataService.saveMultiReportToDatabase(request, response, taskConfigId, taskCycleMark);

                        // 保存成功后，更新Tb_VIPTaskList表的AIstatus字段为0
                        updateMultiTaskListAIStatus(taskConfigId, taskCycleMark, 0);

                        successCount++;
                        log.info("成功处理月份：{}，进度：{}/{}，已更新AIstatus", month, successCount, totalCount);
                    } else {
                        log.error("AI接口调用失败，月份：{}", month);
                    }

                } catch (Exception e) {
                    log.error("处理月份异常，月份：{}，错误：{}", month, e.getMessage(), e);
                    // 单个月份失败不影响其他月份的处理
                }
            }

            int failedCount = totalCount - successCount - skippedCount;
            log.info("VIP多机构月度报告总结任务执行完成，总月份：{}，成功处理：{}，跳过：{}（单机构或无效数据），失败：{}",
                    totalCount, successCount, skippedCount, failedCount);

            // 只要没有失败的任务就返回true（成功处理或正常跳过都是成功）
            return failedCount == 0;

        } catch (Exception e) {
            log.error("VIP多机构月度报告总结任务执行异常", e);
            throw new RuntimeException("VIP多机构月度报告总结任务执行异常", e);
        }
    }

    /**
     * 查询VIP任务数据
     */
    private List<VIPTaskDataDTO> queryVIPTaskData() {
        List<VIPTaskDataDTO> result = new ArrayList<>();

        // 查询大类资产数据
        String categoryAssetSql = buildCategoryAssetSql();
        List<Map> categoryAssetMaps = app.getSqlServer().executeQuery(categoryAssetSql, new ArrayList<>());
        if (!categoryAssetMaps.isEmpty()) {
            List<VIPTaskDataDTO> categoryAssetData = JSON.parseArray(JSON.toJSONString(categoryAssetMaps), VIPTaskDataDTO.class);
            result.addAll(categoryAssetData);
        }

        // 查询行业数据
        String industrySql = buildIndustrySql();
        List<Map> industryMaps = app.getSqlServer().executeQuery(industrySql, new ArrayList<>());
        if (!industryMaps.isEmpty()) {
            List<VIPTaskDataDTO> industryData = JSON.parseArray(JSON.toJSONString(industryMaps), VIPTaskDataDTO.class);
            result.addAll(industryData);
        }

        // 查询资产配置数据
        String assetSql = buildAssetSql();
        List<Map> assetMaps = app.getSqlServer().executeQuery(assetSql, new ArrayList<>());
        if (!assetMaps.isEmpty()) {
            List<VIPTaskDataDTO> assetData = JSON.parseArray(JSON.toJSONString(assetMaps), VIPTaskDataDTO.class);
            result.addAll(assetData);
        }

        // 查询宏观经济数据
        String macroSql = buildMacroSql();
        List<Map> macroMaps = app.getSqlServer().executeQuery(macroSql, new ArrayList<>());
        if (!macroMaps.isEmpty()) {
            List<VIPTaskDataDTO> macroData = JSON.parseArray(JSON.toJSONString(macroMaps), VIPTaskDataDTO.class);
            result.addAll(macroData);
        }

        return result;
    }

    /**
     * 构建大类资产查询SQL
     */
    private String buildCategoryAssetSql() {
        return "SELECT tl.CFHID, tl.TaskConfigID, tl.TaskCycleMark, CommpanyName AS institutionName, " +
                "CASE " +
                "  WHEN tc.TaskCycles = 0 THEN FORMAT(CAST(tl.TaskCycleMark AS date), 'yyyy-MM') " +
                "  WHEN tc.TaskCycles = 1 THEN FORMAT(CAST(SUBSTRING(RIGHT(tl.TaskCycleMark, 14), 1, 8) AS date), 'yyyy-MM') " +
                "  WHEN tc.TaskCycles = 2 THEN FORMAT(CAST(SUBSTRING(RIGHT(tl.TaskCycleMark, 14), 1, 8) AS date), 'yyyy-MM') " +
                "  WHEN tc.TaskCycles = 3 THEN FORMAT(CAST(SUBSTRING(RIGHT(tl.TaskCycleMark, 14), 1, 8) AS date), 'yyyy-MM') " +
                "  ELSE FORMAT(CAST(tl.TaskCycleMark AS date), 'yyyy-MM') " +
                "END as taskMonth, " +
                "CASE " +
                "  WHEN tc.TaskCycles = 0 THEN tl.TaskCycleMark " +
                "  WHEN tc.TaskCycles = 1 THEN FORMAT(CAST(SUBSTRING(RIGHT(tl.TaskCycleMark, 14), 1, 8) AS date), 'yyyy-MM-dd') " +
                "  WHEN tc.TaskCycles = 2 THEN FORMAT(CAST(SUBSTRING(RIGHT(tl.TaskCycleMark, 14), 1, 8) AS date), 'yyyy-MM-dd') " +
                "  WHEN tc.TaskCycles = 3 THEN FORMAT(CAST(SUBSTRING(RIGHT(tl.TaskCycleMark, 14), 1, 8) AS date), 'yyyy-MM-dd') " +
                "  ELSE tl.TaskCycleMark " +
                "END as deadline, " +
                "ts.SubjectType AS categoryType, " +
                "SubjectTitle AS categoryName, " +
                "SubjectCode AS categoryCode, " +
                "JSON_VALUE(pointPrediction,'$.historicalSummary') AS historicalSummary, " +
                "JSON_VALUE(pointPrediction,'$.futureExpectation') AS futureExpectation, " +
                "ts.ID as subjectId " +
                "FROM Tb_VIPTaskList tl " +
                "LEFT JOIN Tb_CFHBaseInfo base ON tl.CFHID = base.CFHID " +
                "LEFT JOIN Tb_VIPTaskConfig tc ON tl.TaskConfigID = tc.ID " +
                "LEFT JOIN Tb_VIPTaskSubject ts ON tl.TaskConfigID = ts.TaskConfigID AND tl.TaskCycleMark = ts.TaskCycleMark " +
                "LEFT JOIN Tb_VIPSubjectAnswer ta ON ts.ID = ta.SubjectID " +
                "WHERE tl.TaskType=4 AND tl.TaskEndTime<CURRENT_TIMESTAMP AND ts.TaskSubjectType='INDUSTRY' AND ta.SubjectType=1 AND base.Status=1 AND tl.Status=1 AND tl.AIstatus=4";
    }

    /**
     * 构建行业查询SQL
     */
    private String buildIndustrySql() {
        return "SELECT tl.CFHID, tl.TaskConfigID, tl.TaskCycleMark, CommpanyName AS institutionName, " +
                "CASE " +
                "  WHEN tc.TaskCycles = 0 THEN FORMAT(CAST(tl.TaskCycleMark AS date), 'yyyy-MM') " +
                "  WHEN tc.TaskCycles = 1 THEN FORMAT(CAST(SUBSTRING(RIGHT(tl.TaskCycleMark, 14), 1, 8) AS date), 'yyyy-MM') " +
                "  WHEN tc.TaskCycles = 2 THEN FORMAT(CAST(SUBSTRING(RIGHT(tl.TaskCycleMark, 14), 1, 8) AS date), 'yyyy-MM') " +
                "  WHEN tc.TaskCycles = 3 THEN FORMAT(CAST(SUBSTRING(RIGHT(tl.TaskCycleMark, 14), 1, 8) AS date), 'yyyy-MM') " +
                "  ELSE FORMAT(CAST(tl.TaskCycleMark AS date), 'yyyy-MM') " +
                "END as taskMonth, " +
                "CASE " +
                "  WHEN tc.TaskCycles = 0 THEN tl.TaskCycleMark " +
                "  WHEN tc.TaskCycles = 1 THEN FORMAT(CAST(SUBSTRING(RIGHT(tl.TaskCycleMark, 14), 1, 8) AS date), 'yyyy-MM-dd') " +
                "  WHEN tc.TaskCycles = 2 THEN FORMAT(CAST(SUBSTRING(RIGHT(tl.TaskCycleMark, 14), 1, 8) AS date), 'yyyy-MM-dd') " +
                "  WHEN tc.TaskCycles = 3 THEN FORMAT(CAST(SUBSTRING(RIGHT(tl.TaskCycleMark, 14), 1, 8) AS date), 'yyyy-MM-dd') " +
                "  ELSE tl.TaskCycleMark " +
                "END as deadline, " +
                "ts.SubjectType AS industryType, " +
                "SubjectTitle AS industryName, " +
                "SubjectCode AS industryCode, " +
                "JSON_VALUE(pointPrediction,'$.historicalSummary') AS historicalSummary, " +
                "JSON_VALUE(pointPrediction,'$.futureExpectation') AS futureExpectation, " +
                "ts.ID as subjectId " +
                "FROM Tb_VIPTaskList tl " +
                "LEFT JOIN Tb_CFHBaseInfo base ON tl.CFHID = base.CFHID " +
                "LEFT JOIN Tb_VIPTaskConfig tc ON tl.TaskConfigID = tc.ID " +
                "LEFT JOIN Tb_VIPTaskSubject ts ON tl.TaskConfigID = ts.TaskConfigID AND tl.TaskCycleMark = ts.TaskCycleMark " +
                "LEFT JOIN Tb_VIPSubjectAnswer ta ON ts.ID = ta.SubjectID " +
                "WHERE tl.TaskType=4 AND tl.TaskEndTime<CURRENT_TIMESTAMP AND ts.TaskSubjectType='INDUSTRY' AND ta.SubjectType=2 AND base.Status=1 AND tl.Status=1 AND tl.AIstatus=4";
    }

    /**
     * 构建资产配置查询SQL
     */
    private String buildAssetSql() {
        return "SELECT tl.CFHID, tl.TaskConfigID, tl.TaskCycleMark, CommpanyName AS institutionName, " +
                "CASE " +
                "  WHEN tc.TaskCycles = 0 THEN FORMAT(CAST(tl.TaskCycleMark AS date), 'yyyy-MM') " +
                "  WHEN tc.TaskCycles = 1 THEN FORMAT(CAST(SUBSTRING(RIGHT(tl.TaskCycleMark, 14), 1, 8) AS date), 'yyyy-MM') " +
                "  WHEN tc.TaskCycles = 2 THEN FORMAT(CAST(SUBSTRING(RIGHT(tl.TaskCycleMark, 14), 1, 8) AS date), 'yyyy-MM') " +
                "  WHEN tc.TaskCycles = 3 THEN FORMAT(CAST(SUBSTRING(RIGHT(tl.TaskCycleMark, 14), 1, 8) AS date), 'yyyy-MM') " +
                "  ELSE FORMAT(CAST(tl.TaskCycleMark AS date), 'yyyy-MM') " +
                "END as taskMonth, " +
                "CASE " +
                "  WHEN tc.TaskCycles = 0 THEN tl.TaskCycleMark " +
                "  WHEN tc.TaskCycles = 1 THEN FORMAT(CAST(SUBSTRING(RIGHT(tl.TaskCycleMark, 14), 1, 8) AS date), 'yyyy-MM-dd') " +
                "  WHEN tc.TaskCycles = 2 THEN FORMAT(CAST(SUBSTRING(RIGHT(tl.TaskCycleMark, 14), 1, 8) AS date), 'yyyy-MM-dd') " +
                "  WHEN tc.TaskCycles = 3 THEN FORMAT(CAST(SUBSTRING(RIGHT(tl.TaskCycleMark, 14), 1, 8) AS date), 'yyyy-MM-dd') " +
                "  ELSE tl.TaskCycleMark " +
                "END as deadline, " +
                "SubjectType AS assetType, " +
                "SubjectTitle AS assetName, " +
                "SubjectCode AS industryCode, " +
                "JSON_VALUE(pointPrediction,'$.lowRisk') AS lowRiskRatio, " +
                "JSON_VALUE(pointPrediction,'$.middleRisk') AS mediumRiskRatio, " +
                "JSON_VALUE(pointPrediction,'$.highRisk') AS highRiskRatio, " +
                "JSON_VALUE(pointPrediction,'$.futureExpectation') AS reason, " +
                "ts.ID as subjectId " +
                "FROM Tb_VIPTaskList tl " +
                "LEFT JOIN Tb_CFHBaseInfo base ON tl.CFHID = base.CFHID " +
                "LEFT JOIN Tb_VIPTaskConfig tc ON tl.TaskConfigID = tc.ID " +
                "LEFT JOIN Tb_VIPTaskSubject ts ON tl.TaskConfigID = ts.TaskConfigID AND tl.TaskCycleMark = ts.TaskCycleMark " +
                "LEFT JOIN Tb_VIPSubjectAnswer ta ON ts.ID = ta.SubjectID " +
                "WHERE tl.TaskType=4 AND tl.TaskEndTime<CURRENT_TIMESTAMP AND ts.TaskSubjectType='ASSET_ALLOCATION' AND base.Status=1 AND tl.Status=1 AND tl.AIstatus=4";
    }

    /**
     * 构建宏观经济查询SQL
     */
    private String buildMacroSql() {
        return "SELECT tl.CFHID, tl.TaskConfigID, tl.TaskCycleMark, CommpanyName as institutionName, " +
                "CASE " +
                "  WHEN tc.TaskCycles = 0 THEN FORMAT(CAST(tl.TaskCycleMark AS date), 'yyyy-MM') " +
                "  WHEN tc.TaskCycles = 1 THEN FORMAT(CAST(SUBSTRING(RIGHT(tl.TaskCycleMark, 14), 1, 8) AS date), 'yyyy-MM') " +
                "  WHEN tc.TaskCycles = 2 THEN FORMAT(CAST(SUBSTRING(RIGHT(tl.TaskCycleMark, 14), 1, 8) AS date), 'yyyy-MM') " +
                "  WHEN tc.TaskCycles = 3 THEN FORMAT(CAST(SUBSTRING(RIGHT(tl.TaskCycleMark, 14), 1, 8) AS date), 'yyyy-MM') " +
                "  ELSE FORMAT(CAST(tl.TaskCycleMark AS date), 'yyyy-MM') " +
                "END as taskMonth, " +
                "CASE " +
                "  WHEN tc.TaskCycles = 0 THEN tl.TaskCycleMark " +
                "  WHEN tc.TaskCycles = 1 THEN FORMAT(CAST(SUBSTRING(RIGHT(tl.TaskCycleMark, 14), 1, 8) AS date), 'yyyy-MM-dd') " +
                "  WHEN tc.TaskCycles = 2 THEN FORMAT(CAST(SUBSTRING(RIGHT(tl.TaskCycleMark, 14), 1, 8) AS date), 'yyyy-MM-dd') " +
                "  WHEN tc.TaskCycles = 3 THEN FORMAT(CAST(SUBSTRING(RIGHT(tl.TaskCycleMark, 14), 1, 8) AS date), 'yyyy-MM-dd') " +
                "  ELSE tl.TaskCycleMark " +
                "END as deadline, " +
                "SubjectType, " +
                "SubjectTitle, " +
                "pointPrediction, " +
                "ts.ID as subjectId " +
                "FROM Tb_VIPTaskList tl " +
                "LEFT JOIN Tb_CFHBaseInfo base ON tl.CFHID = base.CFHID " +
                "LEFT JOIN Tb_VIPTaskConfig tc ON tl.TaskConfigID = tc.ID " +
                "LEFT JOIN Tb_VIPTaskSubject ts ON tl.TaskConfigID = ts.TaskConfigID AND tl.TaskCycleMark = ts.TaskCycleMark " +
                "LEFT JOIN Tb_VIPSubjectAnswer ta ON ts.ID = ta.SubjectID " +
                "WHERE tl.TaskType=4 AND tl.TaskEndTime<CURRENT_TIMESTAMP AND ts.TaskSubjectType='MACRO_ECONOMY' AND base.Status=1 AND tl.Status=1 AND tl.AIstatus=4";
    }

    /**
     * 按月份分组数据
     */
    private Map<String, List<VIPTaskDataDTO>> groupDataByMonth(List<VIPTaskDataDTO> taskDataList) {
        return taskDataList.stream()
                .filter(data -> StringUtils.isNotEmpty(data.getTaskMonth()))
                .collect(Collectors.groupingBy(VIPTaskDataDTO::getTaskMonth));
    }

    /**
     * 构建多机构请求数据
     */
    private MultiMonthlyReportRequestDTO buildMultiRequest(List<VIPTaskDataDTO> dataList) {
        if (CollectionUtils.isEmpty(dataList)) {
            log.warn("数据列表为空，无法构建多机构请求");
            return null;
        }

        // 按机构分组
        Map<String, List<VIPTaskDataDTO>> institutionGroups = dataList.stream()
                .filter(data -> StringUtils.isNotEmpty(data.getCfhid()) && StringUtils.isNotEmpty(data.getInstitutionName()))
                .collect(Collectors.groupingBy(VIPTaskDataDTO::getCfhid));

        if (institutionGroups.isEmpty()) {
            log.warn("没有有效的机构数据");
            return null;
        }

        log.info("发现{}个机构的数据：{}", institutionGroups.size(),
                institutionGroups.keySet().stream()
                        .map(cfhId -> {
                            String institutionName = institutionGroups.get(cfhId).get(0).getInstitutionName();
                            return cfhId + "(" + institutionName + ")";
                        })
                        .collect(Collectors.joining(", ")));

        // 如果只有一个机构，提前返回null（在上层会被跳过）
        if (institutionGroups.size() < 2) {
            log.info("只有{}个机构，不满足多机构报告生成条件", institutionGroups.size());
            return null;
        }

        MultiMonthlyReportRequestDTO request = new MultiMonthlyReportRequestDTO();
        List<MultiMonthlyReportRequestDTO.Institution> institutions = new ArrayList<>();

        // 获取截止时间（使用第一条数据的截止时间）
        VIPTaskDataDTO firstData = dataList.get(0);
        request.setDeadline(firstData.getDeadline() != null ?
                TimeUtil.dateToStr(firstData.getDeadline(), TimeUtil.FORMAT_YYYY_MM_DD) : null);

        // 为每个机构构建数据
        for (Map.Entry<String, List<VIPTaskDataDTO>> entry : institutionGroups.entrySet()) {
            String cfhId = entry.getKey();
            List<VIPTaskDataDTO> institutionData = entry.getValue();

            MultiMonthlyReportRequestDTO.Institution institution = buildInstitutionData(cfhId, institutionData);
            if (institution != null) {
                institutions.add(institution);
            }
        }

        request.setInstitutions(institutions);
        return request;
    }

    /**
     * 构建单个机构数据
     */
    private MultiMonthlyReportRequestDTO.Institution buildInstitutionData(String cfhId, List<VIPTaskDataDTO> institutionData) {
        if (CollectionUtils.isEmpty(institutionData)) {
            return null;
        }

        VIPTaskDataDTO firstData = institutionData.get(0);
        MultiMonthlyReportRequestDTO.Institution institution = new MultiMonthlyReportRequestDTO.Institution();
        institution.setCfhId(cfhId);
        institution.setInstitutionName(firstData.getInstitutionName());
        institution.setTaskMonth(firstData.getTaskMonth());

        // 构建大类资产数据
        List<MultiMonthlyReportRequestDTO.CategoryAsset> categoryAssets = new ArrayList<>();
        for (VIPTaskDataDTO data : institutionData) {
            if (StringUtils.isNotEmpty(data.getCategoryType()) && StringUtils.isNotEmpty(data.getCategoryName())) {
                MultiMonthlyReportRequestDTO.CategoryAsset categoryAsset = new MultiMonthlyReportRequestDTO.CategoryAsset();
                categoryAsset.setSubjectId(data.getSubjectId());
                categoryAsset.setCategoryType(data.getCategoryType());
                categoryAsset.setCategoryName(data.getCategoryName());
                categoryAsset.setCategoryCode(data.getCategoryCode());
                categoryAsset.setHistoricalSummary(data.getHistoricalSummary());
                categoryAsset.setFutureExpectation(data.getFutureExpectation());
                categoryAssets.add(categoryAsset);
            }
        }
        institution.setCategoryAssets(categoryAssets);

        // 构建行业数据
        List<MultiMonthlyReportRequestDTO.Industry> industries = new ArrayList<>();
        for (VIPTaskDataDTO data : institutionData) {
            if (StringUtils.isNotEmpty(data.getIndustryType()) && StringUtils.isNotEmpty(data.getIndustryName())) {
                MultiMonthlyReportRequestDTO.Industry industry = new MultiMonthlyReportRequestDTO.Industry();
                industry.setSubjectId(data.getSubjectId());
                industry.setIndustryType(data.getIndustryType());
                industry.setIndustryName(data.getIndustryName());
                industry.setIndustryCode(data.getIndustryCode());
                industry.setHistoricalSummary(data.getHistoricalSummary());
                industry.setFutureExpectation(data.getFutureExpectation());
                industries.add(industry);
            }
        }
        institution.setIndustries(industries);

        // 构建资产配置数据
        List<MultiMonthlyReportRequestDTO.AssetAllocation> assetAllocations = new ArrayList<>();
        for (VIPTaskDataDTO data : institutionData) {
            if (StringUtils.isNotEmpty(data.getAssetType()) && StringUtils.isNotEmpty(data.getAssetName())) {
                MultiMonthlyReportRequestDTO.AssetAllocation asset = new MultiMonthlyReportRequestDTO.AssetAllocation();
                asset.setSubjectId(data.getSubjectId());
                asset.setAssetType(data.getAssetType());
                asset.setAssetName(data.getAssetName());
                asset.setLowRiskRatio(parseDouble(data.getLowRiskRatio()));
                asset.setMediumRiskRatio(parseDouble(data.getMediumRiskRatio()));
                asset.setHighRiskRatio(parseDouble(data.getHighRiskRatio()));
                asset.setReason(data.getReason());
                assetAllocations.add(asset);
            }
        }
        institution.setAssetAllocations(assetAllocations);

        // 构建宏观经济数据
        List<MultiMonthlyReportRequestDTO.MacroEconomic> macroEconomics = new ArrayList<>();
        Map<String, List<VIPTaskDataDTO>> macroGrouped = institutionData.stream()
                .filter(data -> StringUtils.isNotEmpty(data.getSubjectType()) && StringUtils.isNotEmpty(data.getPointPrediction()))
                .collect(Collectors.groupingBy(VIPTaskDataDTO::getSubjectType));

        for (Map.Entry<String, List<VIPTaskDataDTO>> entry : macroGrouped.entrySet()) {
            MultiMonthlyReportRequestDTO.MacroEconomic macro = new MultiMonthlyReportRequestDTO.MacroEconomic();
            macro.setSubjectId(entry.getValue().get(0).getSubjectId());
            macro.setAspectDetail(entry.getKey());

            List<MultiMonthlyReportRequestDTO.MacroEconomicItem> items = new ArrayList<>();
            for (VIPTaskDataDTO data : entry.getValue()) {
                MultiMonthlyReportRequestDTO.MacroEconomicItem item = new MultiMonthlyReportRequestDTO.MacroEconomicItem();
                item.setSubtitle(data.getSubjectTitle());
                item.setContent(data.getPointPrediction());
                items.add(item);
            }
            macro.setItems(items);
            macroEconomics.add(macro);
        }
        institution.setMacroEconomics(macroEconomics);

        return institution;
    }

    /**
     * 解析Double值
     */
    private Double parseDouble(String value) {
        if (StringUtils.isEmpty(value)) {
            return null;
        }
        try {
            return Double.parseDouble(value);
        } catch (NumberFormatException e) {
            log.warn("解析Double值失败：{}", value);
            return null;
        }
    }

    /**
     * 异步调用多机构AI服务
     */
    private MultiMonthlyReportResponseDTO callMultiAIServiceAsync(MultiMonthlyReportRequestDTO request, String taskConfigId, String taskCycleMark) {
        try {
            // 第一次调用：提交任务
            Object firstResponse = multiMonthlyReportAIApiService.getMultiMonthlySummary(request);

            // 检查响应是否包含taskId（异步模式）
            if (firstResponse instanceof Map) {
                Map<String, Object> responseMap = (Map<String, Object>) firstResponse;
                Object taskIdObj = responseMap.get("taskId");
                Object statusObj = responseMap.get("status");
                String taskId = taskIdObj != null ? taskIdObj.toString() : null;
                String status = statusObj != null ? statusObj.toString() : null;

                if ("PROCESSING".equals(status) && taskId != null) {
                    log.info("多机构AI任务已提交，taskId：{}，开始轮询结果", taskId);

                    // 轮询获取结果
                    return pollMultiAIResult(taskId, taskConfigId, taskCycleMark);
                }
            }

            // 如果不是异步模式，直接返回结果
            if (firstResponse instanceof MultiMonthlyReportResponseDTO) {
                return (MultiMonthlyReportResponseDTO) firstResponse;
            }

            throw new RuntimeException("多机构AI服务返回格式异常");

        } catch (Exception e) {
            log.error("调用多机构AI服务失败，taskConfigId：{}，错误：{}", taskConfigId, e.getMessage(), e);
            throw new RuntimeException("调用多机构AI服务失败：" + e.getMessage(), e);
        }
    }

    /**
     * 轮询多机构AI结果
     */
    private MultiMonthlyReportResponseDTO pollMultiAIResult(String taskId, String taskConfigId, String taskCycleMark) {
        int maxAttempts = 60; // 最多轮询5分钟（60次 * 5秒）
        int attempt = 0;

        while (attempt < maxAttempts) {
            try {
                // 等待5秒
                Thread.sleep(5000);
                attempt++;

                log.debug("轮询多机构AI结果，taskId：{}，第{}次尝试", taskId, attempt);

                // 调用轮询接口
                Object pollResponse = multiMonthlyReportAIApiService.pollMultiMonthlySummary(taskId);

                log.debug("多机构轮询响应类型：{}，内容：{}",
                    pollResponse != null ? pollResponse.getClass().getSimpleName() : "null",
                    pollResponse);

                if (pollResponse instanceof Map) {
                    Map<String, Object> responseMap = (Map<String, Object>) pollResponse;
                    Object statusObj = responseMap.get("status");
                    String status = statusObj != null ? statusObj.toString() : null;

                    if ("COMPLETED".equals(status)) {
                        // 任务完成，提取结果
                        Object data = responseMap.get("data");
                        if (data instanceof MultiMonthlyReportResponseDTO) {
                            log.info("多机构AI任务完成，taskId：{}，耗时：{}秒", taskId, attempt * 5);
                            return (MultiMonthlyReportResponseDTO) data;
                        } else if (data instanceof Map) {
                            // 如果data是Map，需要转换为MultiMonthlyReportResponseDTO
                            return convertMapToMultiResponse((Map<String, Object>) data);
                        } else {
                            throw new RuntimeException("多机构AI响应数据格式异常，data字段类型：" + (data != null ? data.getClass().getSimpleName() : "null"));
                        }
                    } else if ("FAILED".equals(status)) {
                        Object messageObj = responseMap.get("message");
                        String message = messageObj != null ? messageObj.toString() : "未知错误";
                        throw new RuntimeException("多机构AI任务失败：" + message);
                    }
                    // 如果是PROCESSING状态，继续轮询
                    log.debug("多机构AI任务处理中，taskId：{}，状态：{}", taskId, status);
                }

            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                throw new RuntimeException("多机构轮询被中断", e);
            } catch (Exception e) {
                log.error("轮询多机构AI结果失败，taskId：{}，第{}次尝试，错误：{}", taskId, attempt, e.getMessage());
                if (attempt >= maxAttempts) {
                    throw new RuntimeException("轮询多机构AI结果超时：" + e.getMessage(), e);
                }
            }
        }

        throw new RuntimeException("轮询多机构AI结果超时，taskId：" + taskId);
    }

    /**
     * 将Map转换为MultiMonthlyReportResponseDTO
     */
    private MultiMonthlyReportResponseDTO convertMapToMultiResponse(Map<String, Object> dataMap) {
        try {
            // 使用JSON转换
            String jsonString = JSON.toJSONString(dataMap);
            return JSON.parseObject(jsonString, MultiMonthlyReportResponseDTO.class);
        } catch (Exception e) {
            log.error("转换多机构AI响应数据失败", e);
            throw new RuntimeException("转换多机构AI响应数据失败：" + e.getMessage(), e);
        }
    }

    /**
     * 更新Tb_VIPTaskList表的AIstatus字段（多机构）
     *
     * @param taskConfigId 任务配置ID
     * @param taskCycleMark 任务周期标记
     * @param status 要设置的状态值
     */
    private void updateMultiTaskListAIStatus(String taskConfigId, String taskCycleMark, int status) {
        try {
            if (StringUtils.isEmpty(taskConfigId) || StringUtils.isEmpty(taskCycleMark)) {
                log.warn("更新多机构AIstatus失败：缺少必要字段");
                return;
            }

            String sql = "UPDATE Tb_VIPTaskList SET AIstatus = ? WHERE TaskConfigID = ? AND TaskCycleMark = ?";
            List<Object> params = new ArrayList<>();
            params.add(status);
            params.add(taskConfigId);
            params.add(taskCycleMark);

            boolean result = app.getSqlServer().execute(sql, params);
            if (result) {
                log.debug("成功更新多机构AIstatus：TaskConfigID={}，TaskCycleMark={}，AIstatus={}",
                    taskConfigId, taskCycleMark, status);
            } else {
                log.warn("更新多机构AIstatus失败：TaskConfigID={}，TaskCycleMark={}",
                    taskConfigId, taskCycleMark);
            }
        } catch (Exception e) {
            log.error("更新多机构AIstatus异常：TaskConfigID={}，错误：{}",
                taskConfigId, e.getMessage(), e);
        }
    }
}
