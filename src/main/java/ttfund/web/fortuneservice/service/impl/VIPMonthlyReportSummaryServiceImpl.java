package ttfund.web.fortuneservice.service.impl;

import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import org.springframework.util.CollectionUtils;
import ttfund.web.fortuneservice.config.App;

import ttfund.web.fortuneservice.manager.MonthlyReportAIApiService;
import ttfund.web.fortuneservice.model.dto.*;

import ttfund.web.fortuneservice.service.VIPAIReportDataService;
import ttfund.web.fortuneservice.service.VIPMonthlyReportSummaryService;
import ttfund.web.fortuneservice.utils.TimeUtil;

import java.util.*;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * VIP月度报告总结服务实现类
 */
@Service
@Slf4j
public class VIPMonthlyReportSummaryServiceImpl implements VIPMonthlyReportSummaryService {

    @Autowired
    private App app;

    /**
     * 并发处理线程数，默认为3
     */
    @Value("${vip.monthly.report.concurrent.threads:3}")
    private int concurrentThreads;


    @Autowired
    private MonthlyReportAIApiService monthlyReportAIApiService;

    @Autowired
    private VIPAIReportDataService vipAIReportDataService;

    @Override
    public boolean generateMonthlyReportSummary(String param) {
        try {
            log.info("开始执行VIP月度报告总结任务，参数：{}", param);

            // 1. 查询数据库获取VIP任务数据
            List<VIPTaskDataDTO> taskDataList = queryVIPTaskData();
            if (CollectionUtils.isEmpty(taskDataList)) {
                log.warn("未查询到需要处理的VIP任务数据");
                return true;
            }

            // 2. 按机构和月份分组处理
            Map<String, List<VIPTaskDataDTO>> groupedData = groupDataByInstitutionAndMonth(taskDataList);

            // 3. 并发为每个分组生成报告总结
            int totalCount = groupedData.size();
            log.info("开始并发处理{}个机构，并发线程数：{}", totalCount, concurrentThreads);

            // 创建线程池
            ExecutorService executor = Executors.newFixedThreadPool(concurrentThreads);

            // 用于统计结果
            AtomicInteger successCount = new AtomicInteger(0);
            AtomicInteger index = new AtomicInteger(1); // 批次内序号，从1开始

            // 创建任务列表
            List<CompletableFuture<Void>> futures = new ArrayList<>();

            for (Map.Entry<String, List<VIPTaskDataDTO>> entry : groupedData.entrySet()) {
                String key = entry.getKey();
                List<VIPTaskDataDTO> data = entry.getValue();

                // 为每个分组创建异步任务
                CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                    try {
                        // 获取当前任务的序号
                        int currentIndex = index.getAndIncrement();

                        VIPTaskDataDTO firstData = data.get(0);
                        log.info("处理分组：{}，机构：{}，月份：{}，CFHID：{}，TaskConfigID：{}，数据条数：{}",
                                key, firstData.getInstitutionName(), firstData.getTaskMonth(),
                                firstData.getCfhid(), firstData.getTaskConfigId(), data.size());

                        // 构建请求数据（无事务）
                        // 使用已获取的firstData的CFHID、TaskConfigID和TaskCycleMark
                        String cfhid = firstData.getCfhid();
                        String taskConfigId = firstData.getTaskConfigId();
                        String taskCycleMark = firstData.getTaskCycleMark();

                        MonthlyReportRequestDTO request = buildRequest(data);
                        if (request == null) {
                            log.warn("构建请求数据失败，跳过分组：{}", key);
                            return;
                        }

                        // 调用AI接口（异步模式）
                        MonthlyReportResponseDTO response = callAIServiceAsync(request, cfhid, taskConfigId, taskCycleMark, currentIndex);
                        if (response != null) {
                            // 调用独立的数据服务保存（事务在数据服务中处理）
                            vipAIReportDataService.saveReportToDatabase(request, response, cfhid, taskConfigId, taskCycleMark, currentIndex);

                            // 保存成功后，更新Tb_VIPTaskList表的AIstatus字段为4
                            updateTaskListAIStatus(cfhid, taskConfigId, taskCycleMark, 4);

                            int currentSuccess = successCount.incrementAndGet();
                            log.info("成功处理分组：{}，进度：{}/{}，序号：{}，已更新AIstatus", key, currentSuccess, totalCount, currentIndex);
                        } else {
                            log.error("AI接口调用失败，分组：{}", key);
                        }

                    } catch (Exception e) {
                        // 单个分组失败不影响其他分组的处理
                        log.error("处理分组异常，分组：{}，错误：{}", key, e.getMessage(), e);
                    }
                }, executor);

                futures.add(future);
            }

            // 等待所有任务完成
            try {
                CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).get();
                log.info("VIP月度报告总结任务执行完成，成功处理：{}/{} 个分组", successCount.get(), totalCount);
            } catch (Exception e) {
                log.error("等待并发任务完成时发生异常", e);
            } finally {
                // 关闭线程池
                executor.shutdown();
                try {
                    if (!executor.awaitTermination(60, TimeUnit.SECONDS)) {
                        executor.shutdownNow();
                    }
                } catch (InterruptedException e) {
                    executor.shutdownNow();
                    Thread.currentThread().interrupt();
                }
            }
            // 至少有一个分组成功才返回true
            return successCount.get() > 0;

        } catch (Exception e) {
            log.error("VIP月度报告总结任务执行异常", e);
            throw new RuntimeException("VIP月度报告总结任务执行异常", e);
        }
    }

    /**
     * 查询VIP任务数据
     */
    private List<VIPTaskDataDTO> queryVIPTaskData() {
        List<VIPTaskDataDTO> result = new ArrayList<>();

        // 查询大类资产数据
        String categoryAssetSql = buildCategoryAssetSql();
        List<Map> categoryAssetMaps = app.getSqlServer().executeQuery(categoryAssetSql, new ArrayList<>());
        if (!categoryAssetMaps.isEmpty()) {
            List<VIPTaskDataDTO> categoryAssetData = JSON.parseArray(JSON.toJSONString(categoryAssetMaps), VIPTaskDataDTO.class);
            result.addAll(categoryAssetData);
        }

        // 查询行业数据
        String industrySql = buildIndustrySql();
        List<Map> industryMaps = app.getSqlServer().executeQuery(industrySql, new ArrayList<>());
        if (!industryMaps.isEmpty()) {
            List<VIPTaskDataDTO> industryData = JSON.parseArray(JSON.toJSONString(industryMaps), VIPTaskDataDTO.class);
            result.addAll(industryData);
        }

        // 查询资产配置数据
        String assetSql = buildAssetSql();
        List<Map> assetMaps = app.getSqlServer().executeQuery(assetSql, new ArrayList<>());
        if (!assetMaps.isEmpty()) {
            List<VIPTaskDataDTO> assetData = JSON.parseArray(JSON.toJSONString(assetMaps), VIPTaskDataDTO.class);
            result.addAll(assetData);
        }

        // 查询宏观经济数据
        String macroSql = buildMacroSql();
        List<Map> macroMaps = app.getSqlServer().executeQuery(macroSql, new ArrayList<>());
        if (!macroMaps.isEmpty()) {
            List<VIPTaskDataDTO> macroData = JSON.parseArray(JSON.toJSONString(macroMaps), VIPTaskDataDTO.class);
            result.addAll(macroData);
        }

        return result;
    }

    /**
     * 构建大类资产查询SQL
     */
    private String buildCategoryAssetSql() {
        return "SELECT tl.CFHID, tl.TaskConfigID, tl.TaskCycleMark, CommpanyName AS institutionName, " +
                "CASE " +
                "  WHEN tc.TaskCycles = 0 THEN FORMAT(CAST(tl.TaskCycleMark AS date), 'yyyy-MM') " +
                "  WHEN tc.TaskCycles = 1 THEN FORMAT(CAST(SUBSTRING(RIGHT(tl.TaskCycleMark, 14), 1, 8) AS date), 'yyyy-MM') " +
                "  WHEN tc.TaskCycles = 2 THEN FORMAT(CAST(SUBSTRING(RIGHT(tl.TaskCycleMark, 14), 1, 8) AS date), 'yyyy-MM') " +
                "  WHEN tc.TaskCycles = 3 THEN FORMAT(CAST(SUBSTRING(RIGHT(tl.TaskCycleMark, 14), 1, 8) AS date), 'yyyy-MM') " +
                "  ELSE FORMAT(CAST(tl.TaskCycleMark AS date), 'yyyy-MM') " +
                "END as taskMonth, " +
                "CASE " +
                "  WHEN tc.TaskCycles = 0 THEN tl.TaskCycleMark " +
                "  WHEN tc.TaskCycles = 1 THEN FORMAT(CAST(SUBSTRING(RIGHT(tl.TaskCycleMark, 14), 1, 8) AS date), 'yyyy-MM-dd') " +
                "  WHEN tc.TaskCycles = 2 THEN FORMAT(CAST(SUBSTRING(RIGHT(tl.TaskCycleMark, 14), 1, 8) AS date), 'yyyy-MM-dd') " +
                "  WHEN tc.TaskCycles = 3 THEN FORMAT(CAST(SUBSTRING(RIGHT(tl.TaskCycleMark, 14), 1, 8) AS date), 'yyyy-MM-dd') " +
                "  ELSE tl.TaskCycleMark " +
                "END as deadline, " +
                "ts.SubjectType AS categoryType, " +
                "SubjectTitle AS categoryName, " +
                "SubjectCode AS categoryCode, " +
                "JSON_VALUE(pointPrediction,'$.historicalSummary') AS historicalSummary, " +
                "JSON_VALUE(pointPrediction,'$.futureExpectation') AS futureExpectation " +
                "FROM Tb_VIPTaskList tl " +
                "LEFT JOIN Tb_CFHBaseInfo base ON tl.CFHID = base.CFHID " +
                "LEFT JOIN Tb_VIPTaskConfig tc ON tl.TaskConfigID = tc.ID " +
                "LEFT JOIN Tb_VIPTaskSubject ts ON tl.TaskConfigID = ts.TaskConfigID AND tl.TaskCycleMark = ts.TaskCycleMark " +
                "LEFT JOIN Tb_VIPSubjectAnswer ta ON ts.ID = ta.SubjectID " +
                "WHERE tl.TaskType=4 AND tl.TaskEndTime<CURRENT_TIMESTAMP AND ts.TaskSubjectType='INDUSTRY' AND ta.SubjectType=1 AND base.Status=1 AND tl.Status=1 AND tl.AIstatus IS NULL";
    }

    /**
     * 构建行业查询SQL
     */
    private String buildIndustrySql() {
        return "SELECT tl.CFHID, tl.TaskConfigID, tl.TaskCycleMark, CommpanyName AS institutionName, " +
                "CASE " +
                "  WHEN tc.TaskCycles = 0 THEN FORMAT(CAST(tl.TaskCycleMark AS date), 'yyyy-MM') " +
                "  WHEN tc.TaskCycles = 1 THEN FORMAT(CAST(SUBSTRING(RIGHT(tl.TaskCycleMark, 14), 1, 8) AS date), 'yyyy-MM') " +
                "  WHEN tc.TaskCycles = 2 THEN FORMAT(CAST(SUBSTRING(RIGHT(tl.TaskCycleMark, 14), 1, 8) AS date), 'yyyy-MM') " +
                "  WHEN tc.TaskCycles = 3 THEN FORMAT(CAST(SUBSTRING(RIGHT(tl.TaskCycleMark, 14), 1, 8) AS date), 'yyyy-MM') " +
                "  ELSE FORMAT(CAST(tl.TaskCycleMark AS date), 'yyyy-MM') " +
                "END as taskMonth, " +
                "CASE " +
                "  WHEN tc.TaskCycles = 0 THEN tl.TaskCycleMark " +
                "  WHEN tc.TaskCycles = 1 THEN FORMAT(CAST(SUBSTRING(RIGHT(tl.TaskCycleMark, 14), 1, 8) AS date), 'yyyy-MM-dd') " +
                "  WHEN tc.TaskCycles = 2 THEN FORMAT(CAST(SUBSTRING(RIGHT(tl.TaskCycleMark, 14), 1, 8) AS date), 'yyyy-MM-dd') " +
                "  WHEN tc.TaskCycles = 3 THEN FORMAT(CAST(SUBSTRING(RIGHT(tl.TaskCycleMark, 14), 1, 8) AS date), 'yyyy-MM-dd') " +
                "  ELSE tl.TaskCycleMark " +
                "END as deadline, " +
                "ts.SubjectType AS industryType, " +
                "SubjectTitle AS industryName, " +
                "SubjectCode AS industryCode, " +
                "JSON_VALUE(pointPrediction,'$.historicalSummary') AS historicalSummary, " +
                "JSON_VALUE(pointPrediction,'$.futureExpectation') AS futureExpectation " +
                "FROM Tb_VIPTaskList tl " +
                "LEFT JOIN Tb_CFHBaseInfo base ON tl.CFHID = base.CFHID " +
                "LEFT JOIN Tb_VIPTaskConfig tc ON tl.TaskConfigID = tc.ID " +
                "LEFT JOIN Tb_VIPTaskSubject ts ON tl.TaskConfigID = ts.TaskConfigID AND tl.TaskCycleMark = ts.TaskCycleMark " +
                "LEFT JOIN Tb_VIPSubjectAnswer ta ON ts.ID = ta.SubjectID " +
                "WHERE tl.TaskType=4 AND tl.TaskEndTime<CURRENT_TIMESTAMP AND ts.TaskSubjectType='INDUSTRY' AND ta.SubjectType=2 AND base.Status=1 AND tl.Status=1 AND tl.AIstatus IS NULL";
    }

    /**
     * 构建资产配置查询SQL
     */
    private String buildAssetSql() {
        return "SELECT tl.CFHID, tl.TaskConfigID, tl.TaskCycleMark, CommpanyName AS institutionName, " +
                "CASE " +
                "  WHEN tc.TaskCycles = 0 THEN FORMAT(CAST(tl.TaskCycleMark AS date), 'yyyy-MM') " +
                "  WHEN tc.TaskCycles = 1 THEN FORMAT(CAST(SUBSTRING(RIGHT(tl.TaskCycleMark, 14), 1, 8) AS date), 'yyyy-MM') " +
                "  WHEN tc.TaskCycles = 2 THEN FORMAT(CAST(SUBSTRING(RIGHT(tl.TaskCycleMark, 14), 1, 8) AS date), 'yyyy-MM') " +
                "  WHEN tc.TaskCycles = 3 THEN FORMAT(CAST(SUBSTRING(RIGHT(tl.TaskCycleMark, 14), 1, 8) AS date), 'yyyy-MM') " +
                "  ELSE FORMAT(CAST(tl.TaskCycleMark AS date), 'yyyy-MM') " +
                "END as taskMonth, " +
                "CASE " +
                "  WHEN tc.TaskCycles = 0 THEN tl.TaskCycleMark " +
                "  WHEN tc.TaskCycles = 1 THEN FORMAT(CAST(SUBSTRING(RIGHT(tl.TaskCycleMark, 14), 1, 8) AS date), 'yyyy-MM-dd') " +
                "  WHEN tc.TaskCycles = 2 THEN FORMAT(CAST(SUBSTRING(RIGHT(tl.TaskCycleMark, 14), 1, 8) AS date), 'yyyy-MM-dd') " +
                "  WHEN tc.TaskCycles = 3 THEN FORMAT(CAST(SUBSTRING(RIGHT(tl.TaskCycleMark, 14), 1, 8) AS date), 'yyyy-MM-dd') " +
                "  ELSE tl.TaskCycleMark " +
                "END as deadline, " +
                "SubjectType AS assetType, " +
                "SubjectTitle AS assetName, " +
                "SubjectCode AS industryCode, " +
                "JSON_VALUE(pointPrediction,'$.lowRisk') AS lowRiskRatio, " +
                "JSON_VALUE(pointPrediction,'$.middleRisk') AS mediumRiskRatio, " +
                "JSON_VALUE(pointPrediction,'$.highRisk') AS highRiskRatio " +
                "FROM Tb_VIPTaskList tl " +
                "LEFT JOIN Tb_CFHBaseInfo base ON tl.CFHID = base.CFHID " +
                "LEFT JOIN Tb_VIPTaskConfig tc ON tl.TaskConfigID = tc.ID " +
                "LEFT JOIN Tb_VIPTaskSubject ts ON tl.TaskConfigID = ts.TaskConfigID AND tl.TaskCycleMark = ts.TaskCycleMark " +
                "LEFT JOIN Tb_VIPSubjectAnswer ta ON ts.ID = ta.SubjectID " +
                "WHERE tl.TaskType=4 AND tl.TaskEndTime<CURRENT_TIMESTAMP AND ts.TaskSubjectType='ASSET_ALLOCATION' AND base.Status=1 AND tl.Status=1 AND tl.AIstatus IS NULL";
    }

    /**
     * 构建宏观经济查询SQL
     */
    private String buildMacroSql() {
        return "SELECT tl.CFHID, tl.TaskConfigID, tl.TaskCycleMark, CommpanyName as institutionName, " +
                "CASE " +
                "  WHEN tc.TaskCycles = 0 THEN FORMAT(CAST(tl.TaskCycleMark AS date), 'yyyy-MM') " +
                "  WHEN tc.TaskCycles = 1 THEN FORMAT(CAST(SUBSTRING(RIGHT(tl.TaskCycleMark, 14), 1, 8) AS date), 'yyyy-MM') " +
                "  WHEN tc.TaskCycles = 2 THEN FORMAT(CAST(SUBSTRING(RIGHT(tl.TaskCycleMark, 14), 1, 8) AS date), 'yyyy-MM') " +
                "  WHEN tc.TaskCycles = 3 THEN FORMAT(CAST(SUBSTRING(RIGHT(tl.TaskCycleMark, 14), 1, 8) AS date), 'yyyy-MM') " +
                "  ELSE FORMAT(CAST(tl.TaskCycleMark AS date), 'yyyy-MM') " +
                "END as taskMonth, " +
                "CASE " +
                "  WHEN tc.TaskCycles = 0 THEN tl.TaskCycleMark " +
                "  WHEN tc.TaskCycles = 1 THEN FORMAT(CAST(SUBSTRING(tl.TaskCycleMark, 11, 8) AS date), 'yyyy-MM-dd') " +
                "  WHEN tc.TaskCycles = 2 THEN FORMAT(CAST(SUBSTRING(tl.TaskCycleMark, 12, 8) AS date), 'yyyy-MM-dd') " +
                "  WHEN tc.TaskCycles = 3 THEN FORMAT(CAST(SUBSTRING(tl.TaskCycleMark, 14, 8) AS date), 'yyyy-MM-dd') " +
                "  ELSE tl.TaskCycleMark " +
                "END as deadline, " +
                "SubjectType, " +
                "SubjectTitle, " +
                "pointPrediction " +
                "FROM Tb_VIPTaskList tl " +
                "LEFT JOIN Tb_CFHBaseInfo base ON tl.CFHID = base.CFHID " +
                "LEFT JOIN Tb_VIPTaskConfig tc ON tl.TaskConfigID = tc.ID " +
                "LEFT JOIN Tb_VIPTaskSubject ts ON tl.TaskConfigID = ts.TaskConfigID AND tl.TaskCycleMark = ts.TaskCycleMark " +
                "LEFT JOIN Tb_VIPSubjectAnswer ta ON ts.ID = ta.SubjectID " +
                "WHERE tl.TaskType=4 AND tl.TaskEndTime<CURRENT_TIMESTAMP AND ts.TaskSubjectType='MACRO_ECONOMY' AND base.Status=1 AND tl.Status=1 AND tl.AIstatus IS NULL";
    }

    /**
     * 按CFHID、TaskConfigID和月份分组数据
     */
    private Map<String, List<VIPTaskDataDTO>> groupDataByInstitutionAndMonth(List<VIPTaskDataDTO> taskDataList) {
        return taskDataList.stream()
                .filter(data -> StringUtils.isNotEmpty(data.getCfhid()) && StringUtils.isNotEmpty(data.getTaskConfigId()))
                .collect(Collectors.groupingBy(data ->
                        data.getCfhid() + "_" + data.getTaskConfigId() + "_" + data.getTaskMonth()));
    }

    /**
     * 构建请求数据
     */
    private MonthlyReportRequestDTO buildRequest(List<VIPTaskDataDTO> dataList) {
        if (CollectionUtils.isEmpty(dataList)) {
            log.warn("数据列表为空，无法构建请求");
            return null;
        }

        VIPTaskDataDTO firstData = dataList.get(0);
        if (firstData == null || StringUtils.isEmpty(firstData.getInstitutionName()) || StringUtils.isEmpty(firstData.getTaskMonth())) {
            log.warn("基础数据不完整，机构名称：{}，任务月份：{}",
                    firstData != null ? firstData.getInstitutionName() : "null",
                    firstData != null ? firstData.getTaskMonth() : "null");
            return null;
        }

        MonthlyReportRequestDTO request = new MonthlyReportRequestDTO();
        request.setInstitutionName(firstData.getInstitutionName());
        request.setTaskMonth(firstData.getTaskMonth());
        request.setDeadline(firstData.getDeadline() != null ?
                TimeUtil.dateToStr(firstData.getDeadline(), TimeUtil.FORMAT_YYYY_MM_DD) : null);

        // 构建大类资产数据
        List<MonthlyReportRequestDTO.CategoryAsset> categoryAssets = new ArrayList<>();
        for (VIPTaskDataDTO data : dataList) {
            if (StringUtils.isNotEmpty(data.getCategoryType()) && StringUtils.isNotEmpty(data.getCategoryName())) {
                MonthlyReportRequestDTO.CategoryAsset categoryAsset = new MonthlyReportRequestDTO.CategoryAsset();
                categoryAsset.setCategoryType(data.getCategoryType());
                categoryAsset.setCategoryName(data.getCategoryName());
                categoryAsset.setCategoryCode(data.getCategoryCode());
                categoryAsset.setHistoricalSummary(data.getHistoricalSummary());
                categoryAsset.setFutureExpectation(data.getFutureExpectation());
                categoryAssets.add(categoryAsset);
            }
        }
        request.setCategoryAssets(categoryAssets);

        // 构建行业数据
        List<MonthlyReportRequestDTO.Industry> industries = new ArrayList<>();
        for (VIPTaskDataDTO data : dataList) {
            if (StringUtils.isNotEmpty(data.getIndustryType()) && StringUtils.isNotEmpty(data.getIndustryName())) {
                MonthlyReportRequestDTO.Industry industry = new MonthlyReportRequestDTO.Industry();
                industry.setIndustryType(data.getIndustryType());
                industry.setIndustryName(data.getIndustryName());
                industry.setIndustryCode(data.getIndustryCode());
                industry.setHistoricalSummary(data.getHistoricalSummary());
                industry.setFutureExpectation(data.getFutureExpectation());
                industries.add(industry);
            }
        }
        request.setIndustries(industries);

        // 构建资产配置数据
        List<MonthlyReportRequestDTO.AssetAllocation> assetAllocations = new ArrayList<>();
        for (VIPTaskDataDTO data : dataList) {
            if (StringUtils.isNotEmpty(data.getAssetType()) && StringUtils.isNotEmpty(data.getAssetName())) {
                MonthlyReportRequestDTO.AssetAllocation asset = new MonthlyReportRequestDTO.AssetAllocation();
                asset.setAssetType(data.getAssetType());
                asset.setAssetName(data.getAssetName());
                asset.setLowRiskRatio(parseDouble(data.getLowRiskRatio()));
                asset.setMediumRiskRatio(parseDouble(data.getMediumRiskRatio()));
                asset.setHighRiskRatio(parseDouble(data.getHighRiskRatio()));
                assetAllocations.add(asset);
            }
        }
        request.setAssetAllocations(assetAllocations);

        // 构建宏观经济数据
        List<MonthlyReportRequestDTO.MacroEconomic> macroEconomics = new ArrayList<>();
        Map<String, List<VIPTaskDataDTO>> macroGrouped = dataList.stream()
                .filter(data -> StringUtils.isNotEmpty(data.getSubjectType()) && StringUtils.isNotEmpty(data.getPointPrediction()))
                .collect(Collectors.groupingBy(VIPTaskDataDTO::getSubjectType));

        for (Map.Entry<String, List<VIPTaskDataDTO>> entry : macroGrouped.entrySet()) {
            MonthlyReportRequestDTO.MacroEconomic macro = new MonthlyReportRequestDTO.MacroEconomic();
            macro.setAspectDetail(entry.getKey());

            List<MonthlyReportRequestDTO.MacroEconomicItem> items = new ArrayList<>();
            for (VIPTaskDataDTO data : entry.getValue()) {
                MonthlyReportRequestDTO.MacroEconomicItem item = new MonthlyReportRequestDTO.MacroEconomicItem();
                item.setSubtitle(data.getSubjectTitle());
                item.setContent(data.getPointPrediction());
                items.add(item);
            }
            macro.setItems(items);
            macroEconomics.add(macro);
        }
        request.setMacroEconomics(macroEconomics);

        return request;
    }

    /**
     * 解析Double值
     */
    private Double parseDouble(String value) {
        if (StringUtils.isEmpty(value)) {
            return null;
        }
        try {
            return Double.parseDouble(value);
        } catch (NumberFormatException e) {
            log.warn("解析Double值失败：{}", value);
            return null;
        }
    }

    /**
     * 异步调用AI服务
     */
    private MonthlyReportResponseDTO callAIServiceAsync(MonthlyReportRequestDTO request, String cfhid, String taskConfigId, String taskCycleMark, int index) {
        try {
            // 第一次调用：提交任务
            Object firstResponse = monthlyReportAIApiService.getMonthlySummary(request);

            log.debug("第一次调用响应类型：{}，内容：{}",
                firstResponse != null ? firstResponse.getClass().getSimpleName() : "null",
                firstResponse);

            // 检查响应是否包含taskId（异步模式）
            if (firstResponse instanceof Map) {
                Map<String, Object> responseMap = (Map<String, Object>) firstResponse;
                Object taskIdObj = responseMap.get("taskId");
                Object statusObj = responseMap.get("status");
                String taskId = taskIdObj != null ? taskIdObj.toString() : null;
                String status = statusObj != null ? statusObj.toString() : null;

                if ("PROCESSING".equals(status) && taskId != null) {
                    log.info("AI任务已提交，taskId：{}，开始轮询结果", taskId);

                    // 轮询获取结果
                    return pollAIResult(taskId, cfhid, taskConfigId, taskCycleMark, index);
                }
            }

            // 如果不是异步模式，直接返回结果
            if (firstResponse instanceof MonthlyReportResponseDTO) {
                return (MonthlyReportResponseDTO) firstResponse;
            }

            throw new RuntimeException("AI服务返回格式异常");

        } catch (Exception e) {
            log.error("调用AI服务失败，cfhid：{}，taskConfigId：{}，错误：{}", cfhid, taskConfigId, e.getMessage(), e);
            throw new RuntimeException("调用AI服务失败：" + e.getMessage(), e);
        }
    }

    /**
     * 轮询AI结果
     */
    private MonthlyReportResponseDTO pollAIResult(String taskId, String cfhid, String taskConfigId, String taskCycleMark, int index) {
        int maxAttempts = 60; // 最多轮询5分钟（60次 * 5秒）
        int attempt = 0;

        while (attempt < maxAttempts) {
            try {
                // 等待5秒
                Thread.sleep(5000);
                attempt++;

                log.debug("轮询AI结果，taskId：{}，第{}次尝试", taskId, attempt);

                // 调用轮询接口
                Object pollResponse = monthlyReportAIApiService.pollMonthlySummary(taskId);

                log.debug("轮询响应类型：{}，内容：{}",
                    pollResponse != null ? pollResponse.getClass().getSimpleName() : "null",
                    pollResponse);

                if (pollResponse instanceof Map) {
                    Map<String, Object> responseMap = (Map<String, Object>) pollResponse;
                    Object statusObj = responseMap.get("status");
                    String status = statusObj != null ? statusObj.toString() : null;

                    if ("COMPLETED".equals(status)) {
                        // 任务完成，提取结果
                        Object data = responseMap.get("data");
                        if (data instanceof MonthlyReportResponseDTO) {
                            log.info("AI任务完成，taskId：{}，耗时：{}秒", taskId, attempt * 5);
                            return (MonthlyReportResponseDTO) data;
                        } else if (data instanceof Map) {
                            // 如果data是Map，需要转换为MonthlyReportResponseDTO
                            return convertMapToResponse((Map<String, Object>) data);
                        } else {
                            throw new RuntimeException("AI响应数据格式异常，data字段类型：" + (data != null ? data.getClass().getSimpleName() : "null"));
                        }
                    } else if ("FAILED".equals(status)) {
                        Object messageObj = responseMap.get("message");
                        String message = messageObj != null ? messageObj.toString() : "未知错误";
                        throw new RuntimeException("AI任务失败：" + message);
                    }
                    // 如果是PROCESSING状态，继续轮询
                    log.debug("AI任务处理中，taskId：{}，状态：{}", taskId, status);
                }

            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                throw new RuntimeException("轮询被中断", e);
            } catch (Exception e) {
                log.error("轮询AI结果失败，taskId：{}，第{}次尝试，错误：{}", taskId, attempt, e.getMessage());
                if (attempt >= maxAttempts) {
                    throw new RuntimeException("轮询AI结果超时：" + e.getMessage(), e);
                }
            }
        }

        throw new RuntimeException("轮询AI结果超时，taskId：" + taskId);
    }

    /**
     * 将Map转换为MonthlyReportResponseDTO
     */
    private MonthlyReportResponseDTO convertMapToResponse(Map<String, Object> dataMap) {
        try {
            // 使用JSON转换
            String jsonString = JSON.toJSONString(dataMap);
            return JSON.parseObject(jsonString, MonthlyReportResponseDTO.class);
        } catch (Exception e) {
            log.error("转换AI响应数据失败", e);
            throw new RuntimeException("转换AI响应数据失败：" + e.getMessage(), e);
        }
    }

    /**
     * 更新Tb_VIPTaskList表的AIstatus字段
     *
     * @param cfhid 财富号ID
     * @param taskConfigId 任务配置ID
     * @param taskCycleMark 任务周期标记
     * @param status 要设置的状态值
     */
    private void updateTaskListAIStatus(String cfhid, String taskConfigId, String taskCycleMark, int status) {
        try {
            if (StringUtils.isEmpty(cfhid) || StringUtils.isEmpty(taskConfigId) || StringUtils.isEmpty(taskCycleMark)) {
                log.warn("更新AIstatus失败：缺少必要字段");
                return;
            }

            String sql = "UPDATE Tb_VIPTaskList SET AIstatus = ? WHERE CFHID = ? AND TaskConfigID = ? AND TaskCycleMark = ?";
            List<Object> params = new ArrayList<>();
            params.add(status);
            params.add(cfhid);
            params.add(taskConfigId);
            params.add(taskCycleMark);

            boolean result = app.getSqlServer().execute(sql, params);
            if (result) {
                log.debug("成功更新AIstatus：CFHID={}，TaskConfigID={}，TaskCycleMark={}，AIstatus={}",
                    cfhid, taskConfigId, taskCycleMark, status);
            } else {
                log.warn("更新AIstatus失败：CFHID={}，TaskConfigID={}，TaskCycleMark={}",
                    cfhid, taskConfigId, taskCycleMark);
            }
        } catch (Exception e) {
            log.error("更新AIstatus异常：CFHID={}，TaskConfigID={}，错误：{}",
                cfhid, taskConfigId, e.getMessage(), e);
        }
    }

}
